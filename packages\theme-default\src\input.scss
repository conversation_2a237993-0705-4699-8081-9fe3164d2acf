// ================================
// Speed UI Input 组件样式 - 简洁版
// ================================

@use './common/var.scss' as *;

/* Speed UI 风格的简洁样式 */
.sp-input {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;
}

.sp-input__wrapper {
  display: flex;
  align-items: center; /* 恢复 center 对齐 */
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 当包含多选标签时，允许高度自适应 */
.sp-input__wrapper:has(.sp-select__inner-content) {
  align-items: stretch;
}



.sp-input__wrapper:hover {
  border-color: #c0c4cc;
}

/* inner 容器样式 - 支持插槽内容 */
.sp-input__inner {
  width: 100%;
  height: 100%;
  flex-grow: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}



/* 当 inner 容器直接包含 input 元素时 */
.sp-input__inner input {
  width: 100%;
  height: 100%;
  flex: 1;
  appearance: none;
  color: #606266;
  font-size: inherit;
  border: none;
  outline: none;
  padding: 0;
  background: none;
  box-sizing: border-box;
}

/* 当 inner 容器包含多选内容时 */
.sp-input__inner:has(.sp-select__inner-content) {
  align-items: stretch;
}

.sp-input__inner input::placeholder {
  color: #a8abb2;
}

.sp-input__prefix,
.sp-input__suffix {
  display: flex;
  align-items: center;
  color: #a8abb2;
  flex-shrink: 0; /* 防止前缀后缀被压缩 */
}

.sp-input__prefix {
  padding-left: 11px;
}

.sp-input__suffix {
  padding-right: 11px;
}

.sp-input__clear,
.sp-input__suffix i {
  cursor: pointer;
  font-size: 14px;
  margin-left: 5px;
  transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.sp-input__clear:hover,
.sp-input__suffix i:hover {
  color: #909399;
}

.sp-input__count {
  color: #909399;
  font-size: 12px;
  margin-left: 5px;
}

/* 尺寸变化 */
.sp-input--large .sp-input__wrapper {
  height: 40px;
  font-size: 16px;
}

.sp-input--large .sp-input__inner {
  padding: 0 15px;
}

.sp-input--medium .sp-input__wrapper {
  height: 32px;
}

.sp-input--medium .sp-input__inner {
  padding: 0 11px;
}

.sp-input--small .sp-input__wrapper {
  height: 24px;
  font-size: 12px;
}

.sp-input--small .sp-input__inner {
  padding: 0 7px;
}

/* 多选模式下允许高度自适应 */
.sp-input__wrapper:has(.sp-select__inner-content) {
  height: auto;
  min-height: inherit;
}

.sp-input__inner:has(.sp-select__inner-content) {
  padding: 4px 11px;
}

.sp-input--large .sp-input__inner:has(.sp-select__inner-content) {
  padding: 4px 15px;
}

.sp-input--small .sp-input__inner:has(.sp-select__inner-content) {
  padding: 2px 7px;
}

/* 有前缀时调整padding */
.sp-input__wrapper:has(.sp-input__prefix) .sp-input__inner {
  padding-left: 0;
}

/* 有后缀时调整padding */
.sp-input__wrapper:has(.sp-input__suffix) .sp-input__inner {
  padding-right: 0;
}

/* 状态样式 - BEM 规范 */
.sp-input--focused .sp-input__wrapper {
  border-color: #409eff;
}

.sp-input--error .sp-input__wrapper {
  border-color: #f56c6c;
}

.sp-input--warning .sp-input__wrapper {
  border-color: #e6a23c;
}

.sp-input--success .sp-input__wrapper {
  border-color: #67c23a;
}

.sp-input--disabled .sp-input__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.sp-input--disabled .sp-input__inner input {
  color: #c0c4cc;
  cursor: not-allowed;
}

.sp-input--disabled .sp-input__inner input::placeholder {
  color: #c0c4cc;
}

.sp-input--readonly .sp-input__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.sp-input--readonly .sp-input__inner input {
  cursor: default;
}

/* 下划线变体样式 */
.sp-input--underlined .sp-input__wrapper {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
  background: transparent;
  padding-bottom: 4px;
  position: relative;
}

.sp-input--underlined .sp-input__wrapper:hover {
  border-bottom-color: #c0c4cc;
}

/* 聚焦时的从中心扩展效果 */
.sp-input--underlined .sp-input__wrapper::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #409eff;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sp-input--underlined.sp-input--focused .sp-input__wrapper::after {
  transform: scaleX(1);
}

.sp-input--underlined.sp-input--error .sp-input__wrapper {
  border-bottom-color: #f56c6c;
}

.sp-input--underlined.sp-input--error .sp-input__wrapper::after {
  background-color: #f56c6c;
}

.sp-input--underlined.sp-input--warning .sp-input__wrapper {
  border-bottom-color: #e6a23c;
}

.sp-input--underlined.sp-input--warning .sp-input__wrapper::after {
  background-color: #e6a23c;
}

.sp-input--underlined.sp-input--success .sp-input__wrapper {
  border-bottom-color: #67c23a;
}

.sp-input--underlined.sp-input--success .sp-input__wrapper::after {
  background-color: #67c23a;
}

.sp-input--underlined.sp-input--disabled .sp-input__wrapper {
  background: transparent;
  border-bottom-color: #e4e7ed;
}

.sp-input--underlined.sp-input--readonly .sp-input__wrapper {
  background: transparent;
  border-bottom-color: #e4e7ed;
}

/* 下划线变体的前缀和后缀样式调整 */
.sp-input--underlined .sp-input__prefix {
  padding-left: 0;
}

.sp-input--underlined .sp-input__suffix {
  padding-right: 0;
}

/* 填充变体样式 - 有背景色和下边框 */
.sp-input--filled .sp-input__wrapper {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 4px 4px 0 0;
  background-color: #f5f7fa;
  padding: 4px 0;
  position: relative;
  transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.sp-input--filled .sp-input__wrapper:hover {
  background-color: #ebeef5;
  border-bottom-color: #c0c4cc;
}

/* 填充变体的聚焦动画效果 - 复用下划线的放射效果 */
.sp-input--filled .sp-input__wrapper::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #409eff;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sp-input--filled.sp-input--focused .sp-input__wrapper::after {
  transform: scaleX(1);
}

/* 填充变体的不同状态 */
.sp-input--filled.sp-input--error .sp-input__wrapper {
  border-bottom-color: #f56c6c;
}

.sp-input--filled.sp-input--error .sp-input__wrapper::after {
  background-color: #f56c6c;
}

.sp-input--filled.sp-input--warning .sp-input__wrapper {
  border-bottom-color: #e6a23c;
}

.sp-input--filled.sp-input--warning .sp-input__wrapper::after {
  background-color: #e6a23c;
}

.sp-input--filled.sp-input--success .sp-input__wrapper {
  border-bottom-color: #67c23a;
}

.sp-input--filled.sp-input--success .sp-input__wrapper::after {
  background-color: #67c23a;
}

.sp-input--filled.sp-input--disabled .sp-input__wrapper {
  background-color: #f0f2f5;
  border-bottom-color: #e4e7ed;
  color: #c0c4cc;
}

.sp-input--filled.sp-input--readonly .sp-input__wrapper {
  background-color: #f0f2f5;
  border-bottom-color: #e4e7ed;
}

/* 填充变体的尺寸调整 - 确保垂直居中 */
.sp-input--filled.sp-input--large .sp-input__wrapper {
  padding: 6px 0;
}

.sp-input--filled.sp-input--medium .sp-input__wrapper {
  padding: 4px 0;
}

.sp-input--filled.sp-input--small .sp-input__wrapper {
  padding: 3px 0;
}

/* Ghost 幽灵变体样式 - 极简透明设计 */
.sp-input--ghost .sp-input__wrapper {
  border: 1px solid transparent;
  border-radius: 4px;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sp-input--ghost .sp-input__wrapper:hover {
  border-color: #dcdfe6;
  background-color: rgba(245, 247, 250, 0.5);
}

.sp-input--ghost.sp-input--focused .sp-input__wrapper {
  border-color: #409eff;
  background-color: #ffffff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.sp-input--ghost.sp-input--error .sp-input__wrapper:hover,
.sp-input--ghost.sp-input--error.sp-input--focused .sp-input__wrapper {
  border-color: #f56c6c;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.1);
}

.sp-input--ghost.sp-input--warning .sp-input__wrapper:hover,
.sp-input--ghost.sp-input--warning.sp-input--focused .sp-input__wrapper {
  border-color: #e6a23c;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.1);
}

.sp-input--ghost.sp-input--success .sp-input__wrapper:hover,
.sp-input--ghost.sp-input--success.sp-input--focused .sp-input__wrapper {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.1);
}

.sp-input--ghost.sp-input--disabled .sp-input__wrapper {
  border-color: transparent;
  background: transparent;
  color: #c0c4cc;
}

.sp-input--ghost.sp-input--readonly .sp-input__wrapper {
  border-color: transparent;
  background: transparent;
}

/* Pill 胶囊变体样式 - 现代圆角设计 */
.sp-input--pill .sp-input__wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 999px;
  background-color: #ffffff;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.sp-input--pill .sp-input__wrapper:hover {
  border-color: #c0c4cc;
}

.sp-input--pill.sp-input--focused .sp-input__wrapper {
  border-color: #409eff;
}

.sp-input--pill.sp-input--error .sp-input__wrapper {
  border-color: #f56c6c;
}

.sp-input--pill.sp-input--warning .sp-input__wrapper {
  border-color: #e6a23c;
}

.sp-input--pill.sp-input--success .sp-input__wrapper {
  border-color: #67c23a;
}

.sp-input--pill.sp-input--disabled .sp-input__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

.sp-input--pill.sp-input--readonly .sp-input__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

/* Square 方形变体样式 - 完全方正的设计 */
.sp-input--square .sp-input__wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 0;
  background-color: #ffffff;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.sp-input--square .sp-input__wrapper:hover {
  border-color: #c0c4cc;
}

.sp-input--square.sp-input--focused .sp-input__wrapper {
  border-color: #409eff;
}

.sp-input--square.sp-input--error .sp-input__wrapper {
  border-color: #f56c6c;
}

.sp-input--square.sp-input--warning .sp-input__wrapper {
  border-color: #e6a23c;
}

.sp-input--square.sp-input--success .sp-input__wrapper {
  border-color: #67c23a;
}

.sp-input--square.sp-input--disabled .sp-input__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

.sp-input--square.sp-input--readonly .sp-input__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

/* Unborder 无边框变体样式 - 极简无边框设计 */
.sp-input--unborder .sp-input__wrapper {
  border: none;
  border-radius: 0;
  background: transparent;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.sp-input--unborder .sp-input__wrapper:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.sp-input--unborder.sp-input--focused .sp-input__wrapper {
  background-color: rgba(64, 158, 255, 0.03);
}

.sp-input--unborder.sp-input--error .sp-input__wrapper {
  background-color: rgba(245, 108, 108, 0.03);
}

.sp-input--unborder.sp-input--warning .sp-input__wrapper {
  background-color: rgba(230, 162, 60, 0.03);
}

.sp-input--unborder.sp-input--success .sp-input__wrapper {
  background-color: rgba(103, 194, 58, 0.03);
}

.sp-input--unborder.sp-input--disabled .sp-input__wrapper {
  background-color: rgba(0, 0, 0, 0.02);
  color: #c0c4cc;
}

.sp-input--unborder.sp-input--readonly .sp-input__wrapper {
  background-color: rgba(0, 0, 0, 0.02);
}

/* ================================
   Effect 效果样式 - 独立的视觉效果
   ================================ */

/* Glow 发光效果 - 可应用于所有变体 */
.sp-input--effect-glow .sp-input__wrapper:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.sp-input--effect-glow.sp-input--focused .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.15),
    0 2px 12px rgba(64, 158, 255, 0.1);
}

/* 不同状态的发光颜色 */
.sp-input--effect-glow.sp-input--error.sp-input--focused .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.15),
    0 2px 12px rgba(245, 108, 108, 0.1);
}

.sp-input--effect-glow.sp-input--warning.sp-input--focused .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.15),
    0 2px 12px rgba(230, 162, 60, 0.1);
}

.sp-input--effect-glow.sp-input--success.sp-input--focused .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.15),
    0 2px 12px rgba(103, 194, 58, 0.1);
}

/* 下划线样式的特殊发光处理 - 移入时无阴影，保持简洁 */
.sp-input--underlined.sp-input--effect-glow .sp-input__wrapper:hover {
  box-shadow: none !important;
}

/* 填充样式的特殊发光处理 - 移入时无阴影，保持简洁 */
.sp-input--filled.sp-input--effect-glow .sp-input__wrapper:hover {
  box-shadow: none !important;
}

.sp-input--filled.sp-input--effect-glow.sp-input--focused .sp-input__wrapper {
  box-shadow: none !important;
}

.sp-input--filled.sp-input--effect-glow.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 填充样式不同状态的底部发光 */
.sp-input--filled.sp-input--effect-glow.sp-input--error.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.sp-input--filled.sp-input--effect-glow.sp-input--warning.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.sp-input--filled.sp-input--effect-glow.sp-input--success.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

/* 禁用状态下不显示发光效果 */
.sp-input--effect-glow.sp-input--disabled .sp-input__wrapper,
.sp-input--effect-glow.sp-input--readonly .sp-input__wrapper {
  box-shadow: none !important;
}

.sp-input--underlined.sp-input--effect-glow.sp-input--focused
  .sp-input__wrapper {
  box-shadow: none !important;
}

.sp-input--underlined.sp-input--effect-glow.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 下划线样式不同状态的底部发光 */
.sp-input--underlined.sp-input--effect-glow.sp-input--error.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.sp-input--underlined.sp-input--effect-glow.sp-input--warning.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.sp-input--underlined.sp-input--effect-glow.sp-input--success.sp-input--focused
  .sp-input__wrapper::after {
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

/* 无边框样式的特殊发光处理 - 通过背景色和阴影营造发光效果 */
.sp-input--unborder.sp-input--effect-glow .sp-input__wrapper:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: rgba(0, 0, 0, 0.03);
}

.sp-input--unborder.sp-input--effect-glow.sp-input--focused .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.15),
    0 2px 12px rgba(64, 158, 255, 0.1);
  background-color: rgba(64, 158, 255, 0.05);
}

/* 无边框样式不同状态的发光 */
.sp-input--unborder.sp-input--effect-glow.sp-input--error.sp-input--focused
  .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.15),
    0 2px 12px rgba(245, 108, 108, 0.1);
  background-color: rgba(245, 108, 108, 0.05);
}

.sp-input--unborder.sp-input--effect-glow.sp-input--warning.sp-input--focused
  .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.15),
    0 2px 12px rgba(230, 162, 60, 0.1);
  background-color: rgba(230, 162, 60, 0.05);
}

.sp-input--unborder.sp-input--effect-glow.sp-input--success.sp-input--focused
  .sp-input__wrapper {
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.15),
    0 2px 12px rgba(103, 194, 58, 0.1);
  background-color: rgba(103, 194, 58, 0.05);
}

/* ================================
   Loading 加载动画样式
   ================================ */

/* 加载条容器 */
.sp-input__loading-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  // background-color: rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border-radius: 0 0 4px 4px;
  z-index: 1;
}

/* 加载进度条 */
.sp-input__loading-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 30%;
  background-color: #409eff;
  border-radius: 1px;
  animation: sp-input-loading 2s ease-in-out infinite;
}

/* 加载动画关键帧 - 左右移动效果 */
@keyframes sp-input-loading {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(350%);
  }
  50.01% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 不同变体的加载条样式调整 */

/* 下划线变体 - 加载条与下划线重合 */
.sp-input--underlined .sp-input__loading-bar {
  bottom: -1px;
  height: 1px;
  background-color: transparent;
  border-radius: 0;
}

/* 填充变体 - 加载条在底部边框位置 */
.sp-input--filled .sp-input__loading-bar {
  bottom: -1px;
  height: 1px;
  background-color: transparent;
  border-radius: 0;
}

/* 幽灵变体 - 加载条稍微突出 */
.sp-input--ghost .sp-input__loading-bar {
  bottom: -2px;
  height: 1px;
  // background-color: rgba(0, 0, 0, 0.04);
  border-radius: 1px;
}

/* 胶囊变体 - 加载条跟随圆角 */
.sp-input--pill .sp-input__loading-bar {
  border-radius: 0 0 999px 999px;
  left: 12px;
  right: 12px;
  bottom: 0px;
  height: 1px;
}

/* 方形变体 - 无圆角 */
.sp-input--square .sp-input__loading-bar {
  border-radius: 0;
}

/* 无边框变体 - 加载条在底部 */
.sp-input--unborder .sp-input__loading-bar {
  bottom: 0;
  height: 1px;
  background-color: transparent;
  border-radius: 0;
}

/* 不同状态的加载条颜色 */
.sp-input--error .sp-input__loading-progress {
  background-color: #f56c6c;
}

.sp-input--warning .sp-input__loading-progress {
  background-color: #e6a23c;
}

.sp-input--success .sp-input__loading-progress {
  background-color: #67c23a;
}

/* 禁用状态下隐藏加载动画（但loading状态除外） */
.sp-input--disabled:not(.sp-input--loading) .sp-input__loading-bar {
  display: none;
}
