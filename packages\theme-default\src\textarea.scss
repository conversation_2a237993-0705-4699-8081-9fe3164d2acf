// ================================
// Speed UI Textarea 组件样式
// ================================

@use './common/var.scss' as *;

/* Speed UI 风格的简洁样式 */
.sp-textarea {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;
}

.sp-textarea__wrapper {
  display: flex; /* 使用 flex 布局让前缀、textarea、后缀并排 */
  align-items: stretch; /* 让所有子元素拉伸到容器高度 */
  position: relative; /* 为后缀提供定位上下文 */
  box-sizing: border-box;
  background-color: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  /* 移除 min-height，让 wrapper 完全跟随 textarea 大小 */
  height: fit-content;
  overflow: visible; /* 确保后缀可见 */
}

.sp-textarea__wrapper:hover {
  border-color: #c0c4cc;
}

.sp-textarea__input-container {
  position: relative;
  width: 100%;
  display: block;
}

/* InputCore 提供的 inner 容器 */
.sp-textarea__inner {
  flex: 1;
  display: block;
  position: relative; /* 为后缀提供定位上下文 */
}

/* 实际的 textarea 元素 */
.sp-textarea__element {
  width: 100%;
  display: block;
  appearance: none;
  color: #606266;
  font-size: inherit;
  font-family: inherit;
  line-height: 1.5;
  border: none;
  outline: none;
  padding: 8px 11px;
  background: none;
  box-sizing: border-box;
  resize: vertical;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 60px;
}

.sp-textarea__element::placeholder {
  color: #a8abb2;
}

.sp-textarea__prefix {
  flex-shrink: 0; /* 不允许收缩 */
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center;
  color: #a8abb2;
  pointer-events: none; /* 防止影响 textarea 的调整大小 */
  padding: 0 0 0 7px; /* 大尺寸左右内边距 */
}

/* Resize 控制 - 简化版本 */
.sp-textarea--resize-vertical .sp-textarea__element {
  resize: vertical;
}

.sp-textarea--resize-horizontal .sp-textarea__element {
  resize: horizontal;
}

.sp-textarea--resize-both .sp-textarea__element {
  resize: both;
}

.sp-textarea--resize-none .sp-textarea__element {
  resize: none;
}

/* InputCore 结构下的后缀样式适配 */
.sp-textarea .sp-textarea__suffix {
  position: absolute;
  bottom: 8px;
  right: 11px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-direction: row;
  color: #a8abb2;
  pointer-events: none;
  z-index: 9999;
  gap: 5px;
}

.sp-textarea .sp-textarea__suffix > * {
  pointer-events: auto;
  position: relative;
  z-index: 10000; /* 确保所有后缀内容都在拖拽角标上方 */
}

/* 确保 textarea 元素本身有正确的定位上下文 */
.sp-textarea .sp-textarea__element {
  position: relative;
  z-index: 0; /* 确保拖拽角标在 textarea 层级，但在后缀下方 */
}

/* resize 控制下的后缀位置调整 - 为拖拽角标留出更多空间 */
.sp-textarea--resize-vertical .sp-textarea__suffix,
.sp-textarea--resize-both .sp-textarea__suffix {
  bottom: 8px;
  right: 20px; /* 为拖拽角标留出更多空间 */
}

/* 有后缀时给 textarea 添加右边距 */
// .sp-textarea--has-suffix .sp-textarea__element {
//   padding-right: 60px !important;
// }

/* 不同尺寸下的后缀位置调整 */
.sp-textarea--large .sp-textarea__suffix {
  bottom: 12px;
  right: 15px;
}

.sp-textarea--large.sp-textarea--has-suffix .sp-textarea__element {
  padding-right: 70px !important;
}

.sp-textarea--medium .sp-textarea__suffix {
  bottom: 8px;
  right: 11px;
}

.sp-textarea--medium.sp-textarea--has-suffix .sp-textarea__element {
  padding-right: 60px !important;
}

.sp-textarea--small .sp-textarea__suffix {
  bottom: 6px;
  right: 7px;
}

.sp-textarea--small.sp-textarea--has-suffix .sp-textarea__element {
  padding-right: 50px !important;
}

/* 为前缀预留空间 */
.sp-textarea--has-prefix .sp-textarea__element {
  padding-left: 0; /* 前缀区域已经有 padding，textarea 不需要额外 padding */
}

/* 不同尺寸的前缀空间调整 */
.sp-textarea--large.sp-textarea--has-prefix .sp-textarea__element {
  padding-left: 0; /* 前缀区域已经有 padding */
}

.sp-textarea--small.sp-textarea--has-prefix .sp-textarea__element {
  padding-left: 0; /* 前缀区域已经有 padding */
}

/* 胶囊变体的前缀空间调整 */
.sp-textarea--pill.sp-textarea--has-prefix .sp-textarea__element {
  padding-left: 0; /* 前缀区域已经有 padding */
}

/* 后缀区域内的图标样式 - 重置所有定位相关样式 */
.sp-textarea .sp-textarea__suffix .clear-icon,
.sp-textarea .sp-textarea__suffix .loading-icon,
.sp-textarea .sp-textarea__suffix .suffix-icon,
.sp-textarea .sp-textarea__suffix .arrow-icon {
  cursor: pointer !important;
  font-size: 14px !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  display: inline-block !important;
  position: static !important;
  z-index: 10001 !important;
  width: 14px !important;
  height: 14px !important;
  flex-shrink: 0;
  vertical-align: top !important;
  transform: none !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
}

.sp-textarea__suffix .clear-icon:hover,
.sp-textarea__suffix .suffix-icon:hover {
  color: #909399;
}

.sp-textarea__clear,
.sp-textarea__suffix-icon {
  cursor: pointer;
  font-size: 14px;
  margin-left: 5px;
  transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.sp-textarea__clear:hover,
.sp-textarea__suffix-icon:hover {
  color: #909399;
}

.sp-textarea__count {
  color: #909399;
  font-size: 12px;
  margin-left: 5px;
  margin-right: 0;
  white-space: nowrap;
  line-height: 1;
  display: inline-flex;
  align-items: center;
}

/* 加载动画 */
.sp-textarea__loading-bar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  overflow: hidden;
  z-index: 1;
}

.sp-textarea__loading-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 30%;
  background: #409eff;
  animation: sp-textarea-loading 2s infinite ease-in-out;
}

@keyframes sp-textarea-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(350%);
  }
}

/* 尺寸变化 */
.sp-textarea--large {
  font-size: 16px;
}

.sp-textarea--large .sp-textarea__element {
  padding: 12px 15px;
  line-height: 1.6;
}

.sp-textarea--large .sp-textarea__prefix {
  padding: 0 10px; /* 大尺寸左右内边距 */
}

/* 大尺寸下有 resize 时的后缀位置调整 */
.sp-textarea--large.sp-textarea--has-suffix.sp-textarea--resize-vertical
  .sp-textarea__suffix,
.sp-textarea--large.sp-textarea--has-suffix.sp-textarea--resize-both
  .sp-textarea__suffix {
  bottom: 10px;
  right: 15px;
}

/* 中尺寸下有 resize 时的后缀位置调整 */
.sp-textarea--medium.sp-textarea--has-suffix.sp-textarea--resize-vertical
  .sp-textarea__suffix,
.sp-textarea--medium.sp-textarea--has-suffix.sp-textarea--resize-both
  .sp-textarea__suffix {
  bottom: 8px;
  right: 16px;
}

.sp-textarea--medium {
  font-size: 14px;
}

.sp-textarea--medium .sp-textarea__element {
  padding: 8px 11px;
  line-height: 1.5;
}

.sp-textarea--medium .sp-textarea__prefix {
  padding: 0 8px; /* 中尺寸左右内边距 */
}

.sp-textarea--small {
  font-size: 12px;
}

.sp-textarea--small .sp-textarea__element {
  padding: 6px 7px;
  line-height: 1.4;
}

.sp-textarea--small .sp-textarea__prefix {
  padding: 0 6px; /* 小尺寸左右内边距 */
}

/* 小尺寸下有 resize 时的后缀位置调整 */
.sp-textarea--small.sp-textarea--has-suffix.sp-textarea--resize-vertical
  .sp-textarea__suffix,
.sp-textarea--small.sp-textarea--has-suffix.sp-textarea--resize-both
  .sp-textarea__suffix {
  bottom: 6px;
  right: 12px;
}

/* 状态样式 - BEM 规范 */
.sp-textarea--focused .sp-textarea__wrapper {
  border-color: #409eff;
}

.sp-textarea--error .sp-textarea__wrapper {
  border-color: #f56c6c;
}

.sp-textarea--warning .sp-textarea__wrapper {
  border-color: #e6a23c;
}

.sp-textarea--success .sp-textarea__wrapper {
  border-color: #67c23a;
}

.sp-textarea--disabled .sp-textarea__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.sp-textarea--disabled .sp-textarea__element {
  color: #c0c4cc;
  cursor: not-allowed;
  resize: none;
}

.sp-textarea--disabled .sp-textarea__element::placeholder {
  color: #c0c4cc;
}

.sp-textarea--disabled:not(.sp-textarea--loading) .sp-textarea__loading-bar {
  display: none;
}

.sp-textarea--readonly .sp-textarea__wrapper {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.sp-textarea--readonly .sp-textarea__element {
  cursor: default;
  resize: none;
}

/* 下划线变体样式 */
.sp-textarea--underlined .sp-textarea__wrapper {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 0;
  background: transparent;
  padding-bottom: 4px;
  position: relative;
}

.sp-textarea--underlined .sp-textarea__wrapper:hover {
  border-bottom-color: #c0c4cc;
}

.sp-textarea--underlined .sp-textarea__wrapper::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #409eff;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sp-textarea--underlined.sp-textarea--focused .sp-textarea__wrapper::after {
  transform: scaleX(1);
}

.sp-textarea--underlined.sp-textarea--error .sp-textarea__wrapper {
  border-bottom-color: #f56c6c;
}

.sp-textarea--underlined.sp-textarea--error .sp-textarea__wrapper::after {
  background-color: #f56c6c;
}

.sp-textarea--underlined .sp-textarea__loading-bar {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
}

/* 填充变体样式 - 与 input 保持一致 */
.sp-textarea--filled .sp-textarea__wrapper {
  border: none;
  border-bottom: 1px solid #dcdfe6;
  border-radius: 4px 4px 0 0;
  background-color: #f5f7fa;
  padding: 4px 0;
  position: relative;
  transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.sp-textarea--filled .sp-textarea__wrapper:hover {
  background-color: #ebeef5;
  border-bottom-color: #c0c4cc;
}

/* 填充变体的聚焦动画效果 - 复用下划线的放射效果 */
.sp-textarea--filled .sp-textarea__wrapper::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #409eff;
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sp-textarea--filled.sp-textarea--focused .sp-textarea__wrapper::after {
  transform: scaleX(1);
}

/* 填充变体的不同状态 */
.sp-textarea--filled.sp-textarea--error .sp-textarea__wrapper {
  border-bottom-color: #f56c6c;
}

.sp-textarea--filled.sp-textarea--error .sp-textarea__wrapper::after {
  background-color: #f56c6c;
}

.sp-textarea--filled.sp-textarea--warning .sp-textarea__wrapper {
  border-bottom-color: #e6a23c;
}

.sp-textarea--filled.sp-textarea--warning .sp-textarea__wrapper::after {
  background-color: #e6a23c;
}

.sp-textarea--filled.sp-textarea--success .sp-textarea__wrapper {
  border-bottom-color: #67c23a;
}

.sp-textarea--filled.sp-textarea--success .sp-textarea__wrapper::after {
  background-color: #67c23a;
}

.sp-textarea--filled.sp-textarea--disabled .sp-textarea__wrapper {
  background-color: #f0f2f5;
  border-bottom-color: #e4e7ed;
  color: #c0c4cc;
}

.sp-textarea--filled.sp-textarea--readonly .sp-textarea__wrapper {
  background-color: #f0f2f5;
  border-bottom-color: #e4e7ed;
}

/* 填充变体的尺寸调整 - 确保垂直居中 */
.sp-textarea--filled.sp-textarea--large .sp-textarea__wrapper {
  padding: 6px 0;
}

.sp-textarea--filled.sp-textarea--medium .sp-textarea__wrapper {
  padding: 4px 0;
}

.sp-textarea--filled.sp-textarea--small .sp-textarea__wrapper {
  padding: 3px 0;
}

.sp-textarea--filled .sp-textarea__loading-bar {
  bottom: -1px;
  height: 1px;
  background-color: transparent;
  border-radius: 0;
}

/* 幽灵变体样式 */
.sp-textarea--ghost .sp-textarea__wrapper {
  background-color: transparent;
  border: 1px dashed #dcdfe6;
}

.sp-textarea--ghost .sp-textarea__wrapper:hover {
  border-color: #c0c4cc;
  background-color: rgba(0, 0, 0, 0.02);
}

.sp-textarea--ghost.sp-textarea--focused .sp-textarea__wrapper {
  border-style: solid;
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.03);
}

.sp-textarea--ghost .sp-textarea__loading-bar {
  left: 1px;
  right: 1px;
  bottom: 1px;
  height: 1px;
}

/* 胶囊变体样式 */
.sp-textarea--pill .sp-textarea__wrapper {
  border-radius: 16px;
}

.sp-textarea--pill .sp-textarea__element {
  padding-left: 16px;
  padding-right: 16px;
}

.sp-textarea--pill .sp-textarea__prefix {
  padding: 0 10px; /* 胶囊变体左右内边距 */
}

.sp-textarea--pill .sp-textarea__suffix {
  bottom: 8px;
  right: 16px;
}

/* 胶囊变体下有 resize 时的后缀位置调整 */
.sp-textarea--pill.sp-textarea--has-suffix.sp-textarea--resize-vertical
  .sp-textarea__suffix,
.sp-textarea--pill.sp-textarea--has-suffix.sp-textarea--resize-both
  .sp-textarea__suffix {
  bottom: 8px;
  right: 16px;
}

.sp-textarea--pill .sp-textarea__loading-bar {
  left: 12px;
  right: 12px;
  bottom: 0;
  height: 1px;
}

/* 方形变体样式 */
.sp-textarea--square .sp-textarea__wrapper {
  border-radius: 0;
}

.sp-textarea--square .sp-textarea__loading-bar {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
}

/* 无边框变体样式 */
.sp-textarea--unborder .sp-textarea__wrapper {
  border: none;
  background-color: transparent;
}

.sp-textarea--unborder .sp-textarea__wrapper:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.sp-textarea--unborder.sp-textarea--focused .sp-textarea__wrapper {
  background-color: rgba(64, 158, 255, 0.03);
}

.sp-textarea--unborder.sp-textarea--error .sp-textarea__wrapper:hover,
.sp-textarea--unborder.sp-textarea--error.sp-textarea--focused
  .sp-textarea__wrapper {
  background-color: rgba(245, 108, 108, 0.03);
}

.sp-textarea--unborder .sp-textarea__loading-bar {
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
}

/* 发光效果 */
.sp-textarea--effect-glow.sp-textarea--focused .sp-textarea__wrapper {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.sp-textarea--effect-glow.sp-textarea--error.sp-textarea--focused
  .sp-textarea__wrapper {
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

.sp-textarea--effect-glow.sp-textarea--warning.sp-textarea--focused
  .sp-textarea__wrapper {
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
}

.sp-textarea--effect-glow.sp-textarea--success.sp-textarea--focused
  .sp-textarea__wrapper {
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

/* 填充样式的特殊发光处理 - 移入时无阴影，保持简洁 */
.sp-textarea--filled.sp-textarea--effect-glow .sp-textarea__wrapper:hover {
  box-shadow: none !important;
}

.sp-textarea--filled.sp-textarea--effect-glow.sp-textarea--focused
  .sp-textarea__wrapper {
  box-shadow: none !important;
}

.sp-textarea--filled.sp-textarea--effect-glow.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 填充样式不同状态的底部发光 */
.sp-textarea--filled.sp-textarea--effect-glow.sp-textarea--error.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.sp-textarea--filled.sp-textarea--effect-glow.sp-textarea--warning.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.sp-textarea--filled.sp-textarea--effect-glow.sp-textarea--success.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

/* 下划线样式的特殊发光处理 */
.sp-textarea--underlined.sp-textarea--effect-glow .sp-textarea__wrapper:hover {
  box-shadow: none !important;
}

.sp-textarea--underlined.sp-textarea--effect-glow.sp-textarea--focused
  .sp-textarea__wrapper {
  box-shadow: none !important;
}

.sp-textarea--underlined.sp-textarea--effect-glow.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 下划线样式不同状态的底部发光 */
.sp-textarea--underlined.sp-textarea--effect-glow.sp-textarea--error.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.sp-textarea--underlined.sp-textarea--effect-glow.sp-textarea--warning.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.sp-textarea--underlined.sp-textarea--effect-glow.sp-textarea--success.sp-textarea--focused
  .sp-textarea__wrapper::after {
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

/* 禁用状态下不显示发光效果 */
.sp-textarea--effect-glow.sp-textarea--disabled .sp-textarea__wrapper,
.sp-textarea--effect-glow.sp-textarea--readonly .sp-textarea__wrapper {
  box-shadow: none !important;
}

/* 自动调整高度样式 */
.sp-textarea--autosize .sp-textarea__element {
  resize: none;
  overflow: hidden;
}
