<!--
  Select.vue - 使用 sp-input 作为基底的选择器组件
  参考 select-field.vue 使用 input-field.vue 的模式
-->

<template>
  <div
    class="select-wrapper"
    @click="handleClick"
  >
    <!-- 直接使用 sp-input，享受所有功能 -->
    <sp-input
      ref="inputRef"
      v-bind="inputProps"
      :value="displayValue"
      @clear="handleClear"
      @focus="handleInputFocus"
      @blur="handleInputBlur"
      @input="baseEventHandlers.handleInput"
      @change="baseEventHandlers.handleChange"
      @keydown="baseEventHandlers.handleKeydown"
      @prefix-click="baseEventHandlers.handlePrefixClick"
      @suffix-click="baseEventHandlers.handleSuffixClick"
    >
      <!-- 内部插槽：多选标签 - 完全替换输入框内容 -->
      <template
        #inner
        v-if="props.multiple"
      >
        <div
          class="sp-select__inner-content"
          tabindex="0"
          @focus="handleMultiSelectFocus"
          @blur="handleMultiSelectBlur"
        >
          <!-- 多选标签 -->
          <div
            v-if="selectedOptions.length > 0"
            class="sp-select__tags"
          >
            <Tag
              v-for="option in selectedOptions"
              :key="getOptionValue(option)"
              :label="getOptionLabel(option)"
              :closable="!props.disabled"
              :size="getTagSize()"
              type="default"
              variant="light"
              class="sp-select__tag"
              @close="handleRemoveTag(option)"
            />
          </div>

          <!-- 占位符 -->
          <span
            v-if="selectedOptions.length === 0"
            class="sp-select__placeholder"
          >
            {{ props.placeholder }}
          </span>
        </div>
      </template>

      <!-- 后缀：下拉箭头 -->
      <template #suffix>
        <!-- 覆盖默认的 suffixIcon，使用我们自己的下拉箭头 -->
        <sp-icon
          name="ChevronDown"
          :size="16"
          :class="arrowClasses"
        />
      </template>
    </sp-input>

    <!-- 下拉选项面板 - 复用现有的 SelectDropdown 组件 -->
    <SelectDropdown
      ref="dropdownRef"
      :visible="isDropdownOpen"
      :options="props.options || []"
      prefix-cls="sp-select"
      :dropdown-style="dropdownStyle"
      :value="props.value"
      :multiple="props.multiple"
      :value-key="props.valueKey || 'value'"
      :label-key="props.labelKey || 'label'"
      :empty-text="props.emptyText || '暂无数据'"
      :variant="props.variant"
      :size="props.size"
      @optionClick="handleSelectOption"
      @mousedown.prevent.stop
    />
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    computed,
    nextTick,
    onMounted,
    onUnmounted,
    defineExpose,
  } from 'vue'
  import SpIcon from '../icon/Icon.vue'
  import SelectDropdown from './internal/SelectDropdown.vue'
  import { Tag } from '../tag'
  import type { SelectProps, SelectEmits } from './select'
  import { selectPropsDefaults } from './select'
  import type { SelectOption } from './select'
  import {
    createInputEventHandlers,
    createEventHandler,
  } from '../../utils/eventFactory'
  import './style'

  /**
   * Select 组件 - 使用 sp-input 作为基底
   *
   * 简单的实现：
   * 1. 直接使用 sp-input 组件
   * 2. 添加下拉选项功能
   * 3. 享受所有现有特性
   */

  const props = withDefaults(defineProps<SelectProps>(), selectPropsDefaults)
  const emit = defineEmits<SelectEmits>()

  // 组件引用
  const inputRef = ref<any>(null)
  const dropdownRef = ref<InstanceType<typeof SelectDropdown> | null>(null)

  // 状态
  const isDropdownOpen = ref(false)
  const dropdownStyle = ref({})
  const isSelecting = ref(false) // 添加选择状态标记
  const isFocused = ref(false) // 手动管理焦点状态

  // 计算属性
  const selectedValues = computed(() => {
    if (props.multiple) {
      return Array.isArray(props.value) ? props.value : []
    }
    return props.value !== undefined ? [props.value] : []
  })

  const selectedOptions = computed(() => {
    if (!props.options) return []
    return selectedValues.value
      .map((value: any) => {
        return props.options?.find(opt => getOptionValue(opt) === value)
      })
      .filter(Boolean) as SelectOption[]
  })

  const displayValue = computed(() => {
    if (props.multiple) {
      // 多选模式下不显示文本，使用标签显示
      return ''
    } else {
      // 单选模式下正常显示选中的文本
      const option = props.options?.find(
        (opt: SelectOption) => getOptionValue(opt) === props.value
      )
      return option ? getOptionLabel(option) : ''
    }
  })

  const arrowClasses = computed(() => {
    const classes = ['select-arrow']
    if (isDropdownOpen.value) {
      classes.push('select-arrow--reverse')
    }
    return classes.join(' ')
  })

  // 计算是否有值（用于清除按钮显示）
  const hasValue = computed(() => {
    if (props.multiple) {
      return selectedValues.value.length > 0
    } else {
      return (
        props.value !== undefined && props.value !== null && props.value !== ''
      )
    }
  })

  // 传递给 sp-input 的属性 - 完全继承 Input 功能
  const inputProps = computed(() => ({
    placeholder: props.placeholder,
    disabled: props.disabled,
    readonly: false, // Select 始终只读
    size: props.size,
    clearable: props.clearable,
    variant: props.variant,
    error: props.validateState === 'error',
    validateState: props.validateState,
    validateMessage: props.validateMessage,
    hasValue: hasValue.value, // 显式传递 hasValue
    focused: isFocused.value, // 手动管理的焦点状态
    // 继承更多 Input 功能
    loading: props.loading || false,
    prefixIcon: props.prefixIcon,
    suffixIcon: props.suffixIcon || 'chevron-down', // 默认下拉箭头
    effect: props.effect || 'none',
    showWordLimit: false, // Select 不需要字数统计
    maxlength: undefined, // Select 不需要长度限制
  }))

  // 工具函数
  const getOptionValue = (option: SelectOption) => {
    return option[props.valueKey || 'value']
  }

  const getOptionLabel = (option: SelectOption) => {
    return option[props.labelKey || 'label']
  }

  // 根据 Select 尺寸获取 Tag 尺寸
  const getTagSize = () => {
    const sizeMap = {
      small: 'small',
      medium: 'medium',
      large: 'large',
    }
    return sizeMap[props.size || 'medium'] as 'small' | 'medium' | 'large'
  }

  // 处理移除标签
  const handleRemoveTag = (option: SelectOption) => {
    if (props.disabled) return

    const optionValue = getOptionValue(option)
    const currentValues = Array.isArray(props.value) ? props.value : []
    const newValue = currentValues.filter(
      (v: string | number) => v !== optionValue
    )

    emit('update:value', newValue)
    emit('change', newValue)
  }

  // 使用事件工厂创建基础事件处理器
  const baseEventHandlers = createInputEventHandlers(emit, inputRef, props)

  // 自定义点击事件处理器
  const handleClick = createEventHandler<MouseEvent>(emit, 'click', {
    before: () => !props.disabled,
    after: event => {
      // 如果点击的是下拉框内部，不处理
      const dropdownEl =
        dropdownRef.value?.$el ||
        (dropdownRef.value as any)?.$el?.querySelector?.('.sp-select__dropdown')
      if (dropdownEl && dropdownEl.contains(event.target as Node)) {
        return
      }

      isDropdownOpen.value = !isDropdownOpen.value

      if (isDropdownOpen.value) {
        isFocused.value = true // 手动设置焦点状态
        emit('focus')
        // 确保输入框也获得焦点
        nextTick(() => {
          updateDropdownPosition()
          if (props.multiple) {
            // 多选模式下，让容器获得焦点
            const wrapperEl = inputRef.value?.$el || inputRef.value?.wrapper
            if (wrapperEl) {
              wrapperEl.focus?.()
            }
          } else {
            // 单选模式下，让输入框获得焦点
            inputRef.value?.focus?.()
          }
        })
      } else {
        // 只有在非选择状态下才触发blur
        if (!isSelecting.value) {
          isFocused.value = false // 手动设置失焦状态
          emit('blur')
        }
      }
    },
  })

  const handleSelectOption = (option: SelectOption) => {
    if (option.disabled) return

    // 设置选择状态标记
    isSelecting.value = true

    const optionValue = getOptionValue(option)
    let newValue: string | number | Array<string | number> | undefined

    if (props.multiple) {
      const currentValues = Array.isArray(props.value) ? props.value : []
      const index = currentValues.indexOf(optionValue)

      if (index > -1) {
        newValue = currentValues.filter(
          (v: string | number) => v !== optionValue
        )
      } else {
        newValue = [...currentValues, optionValue]
      }

      emit('update:value', newValue)
      emit('change', newValue)

      // 多选模式下保持焦点和下拉框打开
      setTimeout(() => {
        isSelecting.value = false
        // 多选模式下，让多选容器重新获得焦点
        if (props.multiple) {
          const multiSelectEl = inputRef.value?.$el?.querySelector?.(
            '.sp-select__inner-content'
          )
          if (multiSelectEl) {
            multiSelectEl.focus()
          }
        } else {
          inputRef.value?.focus?.()
        }
      }, 0)
    } else {
      newValue = optionValue

      emit('update:value', newValue)
      emit('change', newValue)

      // 单选模式：选择后关闭下拉框但保持聚焦状态
      nextTick(() => {
        isDropdownOpen.value = false
        // 保持聚焦状态，不调用 blur

        nextTick(() => {
          isSelecting.value = false
          // 让输入框重新获得焦点，保持聚焦状态
          inputRef.value?.focus?.()
        })
      })
    }
  }

  // 使用事件工厂创建清除事件处理器
  const handleClear = createEventHandler(emit, 'clear', {
    before: () => !props.disabled,
    after: () => {
      const newValue = props.multiple ? [] : undefined
      emit('update:value', newValue)
    },
  })

  // 使用事件工厂创建焦点事件处理器
  const handleInputFocus = createEventHandler<FocusEvent>(emit, 'focus', {
    before: () => {
      if (!isFocused.value) {
        isFocused.value = true
        return true
      }
      return false
    },
  })

  const handleInputBlur = createEventHandler<FocusEvent>(emit, 'blur', {
    before: () => {
      // 单选模式下的失焦处理
      if (!isDropdownOpen.value && !isSelecting.value) {
        isFocused.value = false
        return true
      }
      return false
    },
  })

  // 多选容器焦点事件
  const handleMultiSelectFocus = createEventHandler<FocusEvent>(emit, 'focus', {
    before: () => {
      if (!isFocused.value) {
        isFocused.value = true
        return true
      }
      return false
    },
  })

  const handleMultiSelectBlur = createEventHandler<FocusEvent>(emit, 'blur', {
    before: () => {
      // 简化失焦逻辑，只在真正需要时延迟
      if (isSelecting.value) {
        // 如果正在选择，延迟处理
        setTimeout(() => {
          if (!isDropdownOpen.value) {
            isFocused.value = false
            emit('blur')
          }
        }, 50)
        return false
      } else {
        // 否则立即失焦
        if (!isDropdownOpen.value) {
          isFocused.value = false
          return true
        }
        return false
      }
    },
  })

  // 下拉框位置
  const updateDropdownPosition = () => {
    if (!inputRef.value) return

    const rect = inputRef.value.$el.getBoundingClientRect()

    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999,
      // 禁用动画，避免飘动效果
      transition: 'none',
      transform: 'none',
    }
  }

  // 点击外部关闭
  const handleClickOutside = (event: MouseEvent) => {
    // 如果正在选择中，完全忽略外部点击
    if (isSelecting.value) {
      return
    }

    const target = event.target as Node

    // 检查是否点击了下拉框内部 - 需要找到实际的 DOM 元素
    const dropdownEl =
      dropdownRef.value?.$el ||
      (dropdownRef.value as any)?.$el?.querySelector?.('.sp-select__dropdown')
    if (dropdownEl && dropdownEl.contains(target)) {
      return
    }

    // 检查是否点击了输入框自身
    if (inputRef.value && inputRef.value.$el.contains(target)) {
      return
    }

    // 只有真正的外部点击才关闭并失焦
    if (isDropdownOpen.value) {
      isDropdownOpen.value = false
      isFocused.value = false // 手动设置失焦状态
      emit('blur')
    }
  }

  // 处理窗口滚动和调整大小
  const handleResize = () => {
    if (isDropdownOpen.value) {
      updateDropdownPosition()
    }
  }

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    window.addEventListener('scroll', handleResize, true)
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
    window.removeEventListener('scroll', handleResize, true)
    window.removeEventListener('resize', handleResize)
  })

  // 暴露方法
  const focus = () => inputRef.value?.focus?.()
  const blur = () => inputRef.value?.blur?.()
  const clear = () => handleClear(new Event('clear'))

  defineExpose({
    focus,
    blur,
    clear,
    get select() {
      return inputRef.value?.$el || null
    },
    get wrapper() {
      return inputRef.value?.wrapper || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpSelect',
  }
</script>

<style scoped>
  .select-wrapper {
    position: relative;
    width: 100%;
  }

  .select-arrow {
    color: #6b7280;
    transition: transform 0.2s ease;
    cursor: pointer;
  }

  .select-arrow--reverse {
    transform: rotate(180deg);
  }

  /* 多选内容容器 */
  .sp-select__inner-content {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 100%;
    outline: none; /* 移除默认的焦点轮廓 */
    cursor: text; /* 显示文本光标 */
  }

  /* 多选模式下隐藏输入框 */
  :deep(.sp-input__inner:has(.sp-select__inner-content) input) {
    display: none;
  }

  /* 多选标签容器样式 */
  .sp-select__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    flex: 1;
    min-width: 0;
    align-items: center;
  }

  .sp-select__tag {
    flex-shrink: 0;
  }

  /* 占位符样式 */
  .sp-select__placeholder {
    color: #a8abb2;
    font-size: inherit;
    flex: 1;
  }

  /* 下拉框样式现在由 SelectDropdown 组件提供 */

  /* 覆盖 SelectDropdown 的动画效果，避免飘动 */
  :deep(.sp-select-dropdown-enter-active),
  :deep(.sp-select-dropdown-leave-active) {
    transition: none !important;
  }

  :deep(.sp-select-dropdown-enter-from),
  :deep(.sp-select-dropdown-leave-to) {
    opacity: 1 !important;
    transform: none !important;
  }

  /* 确保输入框在有标签时的样式调整 */
  :deep(.sp-input__inner) {
    flex: 1;
    min-width: 0;
  }

  /* 当有标签时，调整输入框内部布局 */
  :deep(.sp-input__wrapper) {
    min-height: auto;
  }

  :deep(.sp-input__inner-container) {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
    min-height: inherit;
  }
</style>
