<template>
  <div :class="selectClassName">
    <!-- 选择器主体 -->
    <div
      ref="selectRef"
      :class="`${props.prefixCls}__wrapper`"
      @click="handleToggle"
    >
      <!-- 显示区域 -->
      <SelectInput
        :prefixCls="props.prefixCls"
        :multiple="props.multiple"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        :selectedOption="selectedOption"
        :selectedOptions="selectedOptions"
        :valueKey="props.valueKey"
        :labelKey="props.labelKey"
        :size="props.size"
        @removeTag="handleRemoveTag"
      />

      <!-- 后缀图标 -->
      <div :class="`${props.prefixCls}__suffix`">
        <!-- 清除按钮 -->
        <i
          v-if="clearable && hasValueComputed && !disabled"
          :class="[`${props.prefixCls}__icon`, `${props.prefixCls}__clear`]"
          @click.stop="handleClear"
        >
          <sp-icon
            name="Close"
            :size="16"
          />
        </i>

        <!-- 下拉箭头 -->
        <i
          :class="[
            `${props.prefixCls}__icon`,
            `${props.prefixCls}__arrow`,
            { [`${props.prefixCls}__arrow--reverse`]: isDropdownOpen },
          ]"
        >
          <sp-icon
            name="ChevronDown"
            :size="16"
          />
        </i>
      </div>
    </div>

    <!-- 下拉选项 -->
    <SelectDropdown
      :visible="isDropdownOpen"
      :options="actualOptions"
      :prefixCls="props.prefixCls"
      :dropdownStyle="dropdownStyle"
      :value="modelValue"
      :multiple="props.multiple"
      :valueKey="props.valueKey"
      :labelKey="props.labelKey"
      :emptyText="props.emptyText"
      :variant="props.variant"
      @optionClick="handleSelectOption"
    />

    <!-- 验证消息 - 只有在不是 FormItem 内时才显示 -->
    <div
      v-if="!formItemField && computedValidateMessage && showValidateMessage"
      :class="validateMessageClassName"
    >
      {{ computedValidateMessage }}
    </div>

    <!-- 隐藏的默认插槽，用于渲染子组件 -->
    <div style="display: none">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    computed,
    nextTick,
    inject,
    onMounted,
    onUnmounted,
    provide,
  } from 'vue'
  import { classNames } from '../../utils'
  import {
    type SelectProps,
    type SelectEmits,
    type SelectOption,
    selectPropsDefaults,
    getOptionValue,
    isOptionDisabled,
    hasValue,
    getSelectedOption,
    getSelectedOptions,
    updateDropdownPosition,
    multiLevelLinkedSelectManager,
  } from './select'
  import SelectInput from './internal/SelectInput.vue'
  import SelectDropdown from './internal/SelectDropdown.vue'
  import './style'

  const props = withDefaults(defineProps<SelectProps>(), selectPropsDefaults)

  const emit = defineEmits<SelectEmits>()

  const formItemField = inject<any>('spFormItemField', null)

  const selectRef = ref<HTMLDivElement>()
  const isFocused = ref(false)
  const isDropdownOpen = ref(false)
  const dropdownStyle = ref({})

  // 联动选择器相关状态
  const linkedOptions = ref<SelectOption[]>([])

  // 子组件选项数据管理
  const childOptions = ref<SelectOption[]>([])

  // 提供给子组件的选项注册函数
  const registerOption = (option: SelectOption) => {
    const existingIndex = childOptions.value.findIndex(
      item => item.value === option.value
    )
    if (existingIndex > -1) {
      childOptions.value[existingIndex] = option
    } else {
      childOptions.value.push(option)
    }
  }

  const unregisterOption = (value: string | number) => {
    const index = childOptions.value.findIndex(item => item.value === value)
    if (index > -1) {
      childOptions.value.splice(index, 1)
    }
  }

  // 向子组件提供注册函数
  provide('selectRegisterOption', registerOption)
  provide('selectUnregisterOption', unregisterOption)

  // 双向绑定 - 优先使用表单字段，否则使用 props
  const modelValue = computed({
    get: () => {
      if (formItemField) {
        return formItemField.value.value
      }
      return props.value
    },
    set: value => {
      if (formItemField) {
        formItemField.setValue(value)
      }
      emit('update:value', value)
      emit('change', value)

      // 联动处理：如果是联动组件且值发生变化，通知管理器
      if (props.linkedGroup && props.linkedLevel) {
        multiLevelLinkedSelectManager.onValueChange(
          props.linkedGroup,
          props.linkedLevel,
          value as string | number | undefined,
          props.valueKey
        )
      }
    },
  })

  // 验证状态 - 优先使用表单字段
  const computedValidateState = computed(() => {
    if (formItemField && formItemField.validateState) {
      return formItemField.validateState.value
    }
    return props.validateState
  })

  const computedValidateMessage = computed(() => {
    if (formItemField && formItemField.errors.value.length > 0) {
      return formItemField.errors.value[0]
    }
    return props.validateMessage
  })

  // 计算实际使用的选项列表
  const actualOptions = computed(() => {
    if (props.linkedGroup && props.linkedLevel) {
      // 联动组件：使用联动管理器提供的选项
      return linkedOptions.value
    } else if (childOptions.value.length > 0) {
      // 子组件模式：使用子组件注册的选项
      return childOptions.value
    } else {
      // 普通组件：使用传入的 options
      return props.options || []
    }
  })

  // 使用工具函数的计算属性
  const hasValueComputed = computed(() =>
    hasValue(modelValue.value, props.multiple)
  )

  const selectedOption = computed(() =>
    getSelectedOption(
      actualOptions.value,
      modelValue.value,
      props.multiple,
      props.valueKey
    )
  )

  const selectedOptions = computed(() =>
    getSelectedOptions(
      actualOptions.value,
      modelValue.value,
      props.multiple,
      props.valueKey
    )
  )

  const selectClassName = computed(() =>
    classNames(props.prefixCls, `${props.prefixCls}--${props.size}`, {
      [`${props.prefixCls}--disabled`]: props.disabled,
      [`${props.prefixCls}--focused`]: isFocused.value,
      [`${props.prefixCls}--error`]: computedValidateState.value === 'error',
      [`${props.prefixCls}--success`]:
        computedValidateState.value === 'success',
      [`${props.prefixCls}--warning`]:
        computedValidateState.value === 'warning',
      [`${props.prefixCls}--multiple`]: props.multiple,
      [`${props.prefixCls}--clearable`]:
        props.clearable && hasValueComputed.value,
    })
  )

  // 注意：getOptionClassName 已移动到 SelectOption 组件中

  // 验证消息类名
  const validateMessageClassName = computed(() =>
    classNames(
      `${props.prefixCls}__validate-message`,
      `${props.prefixCls}__validate-message--${computedValidateState.value}`
    )
  )

  // 切换下拉框显示
  const handleToggle = () => {
    if (props.disabled) return

    if (isDropdownOpen.value) {
      closeDropdown()
    } else {
      openDropdown()
    }
  }

  // 打开下拉框
  const openDropdown = async () => {
    if (props.disabled) return

    isFocused.value = true
    isDropdownOpen.value = true
    emit('focus')

    await nextTick()
    updateDropdownPositionStyle()
  }

  // 关闭下拉框（但保持聚焦状态）
  const closeDropdown = () => {
    isDropdownOpen.value = false
  }

  // 完全失焦（关闭下拉框并清除聚焦样式）
  const handleBlur = () => {
    isFocused.value = false
    isDropdownOpen.value = false
    emit('blur')

    // 触发表单验证
    if (formItemField) {
      formItemField.handleBlur()
    }
  }

  // 点击外部处理
  const handleClickOutside = (event: Event) => {
    if (selectRef.value && !selectRef.value.contains(event.target as Node)) {
      if (isFocused.value || isDropdownOpen.value) {
        handleBlur()
      }
    }
  }

  // 选择选项
  const handleSelectOption = (option: SelectOption) => {
    if (isOptionDisabled(option)) return

    const value = getOptionValue(option, props.valueKey)

    if (props.multiple) {
      const values = Array.isArray(modelValue.value)
        ? [...modelValue.value]
        : []
      const index = values.indexOf(value)

      if (index > -1) {
        values.splice(index, 1)
      } else {
        values.push(value)
      }

      modelValue.value = values
      // 多选模式：保持下拉框打开状态
    } else {
      modelValue.value = value
      // 单选模式：关闭下拉框但保持聚焦状态
      isDropdownOpen.value = false
    }
  }

  // 移除标签（多选）
  const handleRemoveTag = (option: SelectOption) => {
    if (props.disabled) return

    const value = getOptionValue(option, props.valueKey)
    const values = Array.isArray(modelValue.value) ? [...modelValue.value] : []
    const index = values.indexOf(value)

    if (index > -1) {
      values.splice(index, 1)
      modelValue.value = values
    }
  }

  // 清除
  const handleClear = () => {
    if (props.disabled) return

    modelValue.value = props.multiple ? [] : undefined
    emit('clear')
    // 清除后关闭下拉框但保持聚焦状态
    isDropdownOpen.value = false
  }

  // 更新下拉框位置
  const updateDropdownPositionStyle = () => {
    if (!selectRef.value) return
    dropdownStyle.value = updateDropdownPosition(selectRef.value)
  }

  // 联动选择器相关方法
  const updateLinkedOptions = (options: SelectOption[]) => {
    linkedOptions.value = options
  }

  const clearLinkedValue = () => {
    if (props.multiple) {
      modelValue.value = []
    } else {
      modelValue.value = undefined
    }
  }

  // 键盘事件处理
  const handleKeydown = (event: KeyboardEvent) => {
    if (!isFocused.value || props.disabled) return

    switch (event.key) {
      case 'Escape':
        event.preventDefault()
        handleBlur()
        break
      case 'Enter':
        event.preventDefault()
        break
    }
  }

  // 监听窗口大小变化
  const handleResize = () => {
    if (isDropdownOpen.value) {
      updateDropdownPositionStyle()
    }
  }

  // 联动组件注册和销毁
  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleKeydown)
    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', handleResize)

    // 注册联动组件
    if (props.linkedGroup && props.linkedLevel) {
      multiLevelLinkedSelectManager.registerComponent(
        props.linkedGroup,
        props.linkedLevel,
        updateLinkedOptions,
        clearLinkedValue,
        props.linkedData // 只有第一级需要传递数据
      )
    }
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
    document.removeEventListener('keydown', handleKeydown)
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('scroll', handleResize)

    // 销毁联动组件注册
    if (props.linkedGroup && props.linkedLevel) {
      multiLevelLinkedSelectManager.unregister(
        props.linkedGroup,
        props.linkedLevel
      )
    }
  })
</script>

<script lang="ts">
  export default {
    name: 'SpSelect',
  }
</script>
