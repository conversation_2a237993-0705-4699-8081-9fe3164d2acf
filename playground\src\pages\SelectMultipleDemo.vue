<template>
  <div class="select-multiple-demo">
    <h1>SelectMultiple 多选组件演示</h1>

    <!-- 功能说明 -->
    <div class="feature-description">
      <h3>🎯 SelectMultiple 组件功能</h3>
      <p>sp-select-multiple 组件是基于 Select 组件封装的多选专用组件：</p>
      <ul>
        <li>
          <strong>强制多选</strong>
          : 自动设置 multiple: true
        </li>
        <li>
          <strong>优化默认值</strong>
          : 默认开启清除功能和标签折叠
        </li>
        <li>
          <strong>简化使用</strong>
          : 无需手动设置 multiple 属性
        </li>
        <li>
          <strong>一致API</strong>
          : 与 Select 组件保持相同的API接口
        </li>
      </ul>
    </div>

    <div class="demo-container">
      <section class="demo-section">
        <h3>📋 基础多选</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>基础多选：</label>
            <sp-select-multiple
              v-model:value="basicValue"
              :options="basicOptions"
              placeholder="请选择多个选项"
              style="width: 300px"
            />
            <span class="demo-value">
              选中值: {{ basicValue.length ? basicValue.join(', ') : '无' }}
            </span>
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🏷️ 标签控制</h3>
        <div class="demo-col">
          <div class="demo-item">
            <label>默认标签折叠（最多3个）：</label>
            <sp-select-multiple
              v-model:value="tagValue1"
              :options="manyOptions"
              placeholder="选择多个选项"
              style="width: 350px"
            />
            <sp-select
              v-model:value="tagValue1"
              :options="manyOptions"
              placeholder="选择多个选项"
              style="width: 450px"
            />
          </div>
          <div class="demo-item">
            <label>显示更多标签（最多6个）：</label>
            <sp-select-multiple
              v-model:value="tagValue2"
              :options="manyOptions"
              placeholder="选择多个选项"
              style="width: 550px"
            />
          </div>
          <div class="demo-item">
            <label>不折叠标签：</label>
            <sp-select-multiple
              v-model:value="tagValue3"
              :options="manyOptions"
              placeholder="选择多个选项"
              :collapse-tags="false"
              style="width: 350px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>📏 尺寸变体</h3>
        <div class="demo-col">
          <div class="demo-item">
            <label>小尺寸：</label>
            <sp-select-multiple
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="小尺寸多选"
              size="small"
              style="width: 250px"
            />
          </div>
          <div class="demo-item">
            <label>默认尺寸：</label>
            <sp-select-multiple
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="默认尺寸多选"
              size="medium"
              style="width: 300px"
            />
          </div>
          <div class="demo-item">
            <label>大尺寸：</label>
            <sp-select-multiple
              v-model:value="sizeValue"
              :options="basicOptions"
              placeholder="大尺寸多选"
              size="large"
              style="width: 350px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🚫 禁用状态</h3>
        <div class="demo-row">
          <div class="demo-item">
            <label>禁用多选：</label>
            <sp-select-multiple
              v-model:value="disabledValue"
              :options="basicOptions"
              placeholder="禁用状态"
              disabled
              style="width: 300px"
            />
          </div>
        </div>
      </section>

      <section class="demo-section">
        <h3>🔄 对比普通Select</h3>
        <div class="demo-col">
          <div class="demo-item">
            <label>使用 sp-select multiple：</label>
            <sp-select
              v-model:value="compareValue1"
              :options="basicOptions"
              placeholder="手动设置 multiple"
              multiple
              clearable
              collapse-tags
              :max-tag-count="3"
              style="width: 300px"
            />
          </div>
          <div class="demo-item">
            <label>使用 sp-select-multiple：</label>
            <sp-select-multiple
              v-model:value="compareValue2"
              :options="basicOptions"
              placeholder="自动多选配置"
              style="width: 300px"
            />
          </div>
        </div>
        <p class="comparison-note">
          💡 两者功能相同，但 sp-select-multiple
          自动配置了多选相关的默认值，使用更简便
        </p>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Select as SpSelect,
    Multiple as SpSelectMultiple,
  } from '../../../packages/ui/src/components/select'

  // 演示数据
  const basicValue = ref([])
  const tagValue1 = ref([])
  const tagValue2 = ref([])
  const tagValue3 = ref([])
  const sizeValue = ref([])
  const disabledValue = ref(['option1', 'option2'])
  const compareValue1 = ref([])
  const compareValue2 = ref([])

  // 基础选项
  const basicOptions = [
    { label: '选项一', value: 'option1' },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
    { label: '选项五', value: 'option5' },
  ]

  // 更多选项用于测试标签折叠
  const manyOptions = [
    { label: 'JavaScript', value: 'js' },
    { label: 'TypeScript', value: 'ts' },
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Angular', value: 'angular' },
    { label: 'Node.js', value: 'nodejs' },
    { label: 'Python', value: 'python' },
    { label: 'Java', value: 'java' },
    { label: 'C++', value: 'cpp' },
    { label: 'Go', value: 'go' },
  ]
</script>

<style scoped>
  .select-multiple-demo {
    padding: 20px;
    max-width: 900px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .select-multiple-demo h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
  }

  .feature-description {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
  }

  .feature-description h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
  }

  .feature-description p {
    margin-bottom: 10px;
    color: #6c757d;
    line-height: 1.6;
  }

  .feature-description ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .feature-description li {
    margin-bottom: 8px;
    color: #6c757d;
    line-height: 1.5;
  }

  .feature-description strong {
    color: #495057;
    font-weight: 600;
  }

  .demo-container {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .demo-section {
    margin-bottom: 40px;
  }

  .demo-section:last-child {
    margin-bottom: 0;
  }

  .demo-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
    font-size: 18px;
  }

  .demo-row {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
  }

  .demo-col {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
  }

  .demo-item {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .demo-item label {
    font-weight: 600;
    color: #606266;
    min-width: 180px;
    font-size: 14px;
  }

  .demo-value {
    color: #909399;
    font-size: 12px;
    background: #f5f7fa;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 100px;
  }

  .comparison-note {
    margin-top: 15px;
    padding: 10px;
    background: #e8f4fd;
    border-left: 4px solid #409eff;
    color: #606266;
    font-size: 14px;
    border-radius: 4px;
  }
</style>
