import {
  defineComponent,
  computed,
  ref,
  type PropType,
  type VNode,
  type CSSProperties,
} from 'vue'
import SpIcon from '../icon/Icon.vue'
import { useInputStyles } from '../../composables/useInputStyles'
import type { InputProps, InputType } from './types'

// JSX 类型声明
declare global {
  namespace JSX {
    interface IntrinsicElements {
      div: any
      span: any
      input: any
    }
  }
}

// InputCore 的属性（基于 InputProps 但去掉一些业务逻辑属性）
export interface InputCoreProps extends Omit<InputProps, 'value' | 'defaultValue'> {
  value?: string | number
  // 扩展属性
  hasValue?: boolean
  focused?: boolean
  mode?: 'input' | 'display'
}

// InputCore 的事件
export interface InputCoreEmits {
  click: [event: MouseEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  input: [event: Event]
  change: [event: Event]
  keydown: [event: KeyboardEvent]
  clear: []
  'prefix-click': [event: MouseEvent]
  'suffix-click': [event: MouseEvent]
}

export default defineComponent({
  name: 'InputCore',
  props: {
    // 基础属性
    value: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<InputType>,
      default: 'text',
    },
    placeholder: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    maxlength: {
      type: Number,
      default: undefined,
    },

    // 样式属性
    variant: {
      type: String as PropType<'default' | 'underlined' | 'filled' | 'pill' | 'square' | 'unborder'>,
      default: 'default',
    },
    effect: {
      type: String as PropType<'none' | 'glow'>,
      default: 'none',
    },
    size: {
      type: String as PropType<'small' | 'medium' | 'large'>,
      default: 'medium',
    },

    // 功能属性
    clearable: {
      type: Boolean,
      default: false,
    },
    prefixIcon: {
      type: String,
      default: undefined,
    },
    suffixIcon: {
      type: String,
      default: undefined,
    },
    loading: {
      type: Boolean,
      default: false,
    },

    // 验证相关
    error: {
      type: Boolean,
      default: false,
    },
    validateState: {
      type: String as PropType<'success' | 'warning' | 'error'>,
      default: undefined,
    },
    validateMessage: {
      type: String,
      default: '',
    },

    // 状态属性
    hasValue: {
      type: Boolean,
      default: false,
    },
    focused: {
      type: Boolean,
      default: false,
    },

    // 字数统计
    showWordLimit: {
      type: Boolean,
      default: false,
    },

    // 渲染模式
    mode: {
      type: String as PropType<'input' | 'display'>,
      default: 'input',
    },
  },
  emits: {
    click: (_event: MouseEvent) => true,
    focus: (_event: FocusEvent) => true,
    blur: (_event: FocusEvent) => true,
    input: (_event: Event) => true,
    change: (_event: Event) => true,
    keydown: (_event: KeyboardEvent) => true,
    clear: () => true,
    'prefix-click': (_event: MouseEvent) => true,
    'suffix-click': (_event: MouseEvent) => true,
  },
  setup(props, { emit, slots, expose }) {
    const wrapperRef = ref<HTMLDivElement>()
    const inputRef = ref<HTMLInputElement>()

    // 计算状态
    const computedDisabled = computed(() => props.disabled)
    const isFocused = computed(() => props.focused)
    const hasValue = computed(() => props.hasValue)
    const showPrefix = computed(() => !!props.prefixIcon || !!slots.prefix)
    const showSuffix = computed(() => {
      return (
        !!props.suffixIcon ||
        !!slots.suffix ||
        (props.clearable && hasValue.value && !props.disabled) ||
        props.loading ||
        (props.showWordLimit && props.maxlength)
      )
    })

    // 使用样式 composable
    const {
      rootClasses,
      wrapperClasses,
      innerClasses,
      prefixClasses,
      suffixClasses,
      prefixIconClasses,
      suffixIconClasses,
      clearIconClasses,
      wordCountClasses,
      iconSize,
    } = useInputStyles(props as any, {
      computedDisabled,
      isFocused,
      hasValue,
      showPrefix,
      showSuffix,
    })

    // 显示清除按钮
    const showClearIcon = computed(() => {
      return props.clearable && hasValue.value && !props.disabled && !props.readonly
    })

    // 字数统计
    const wordCount = computed(() => {
      const value = props.value
      return typeof value === 'string' ? value.length : 0
    })

    // 事件处理
    const handleWrapperClick = (event: MouseEvent) => {
      if (props.disabled || props.readonly) return
      inputRef.value?.focus()
      emit('click', event)
    }

    const handlePrefixClick = (event: MouseEvent) => {
      event.stopPropagation()
      emit('prefix-click', event)
    }

    const handleSuffixClick = (event: MouseEvent) => {
      event.stopPropagation()
      emit('suffix-click', event)
    }

    const handleClear = (event: MouseEvent) => {
      event.stopPropagation()
      emit('clear')
      inputRef.value?.focus()
    }

    const handleInput = (event: Event) => {
      emit('input', event)
    }

    const handleChange = (event: Event) => {
      emit('change', event)
    }

    const handleFocus = (event: FocusEvent) => {
      emit('focus', event)
    }

    const handleBlur = (event: FocusEvent) => {
      emit('blur', event)
    }

    const handleKeydown = (event: KeyboardEvent) => {
      emit('keydown', event)
    }

    // 渲染前缀区域
    const renderPrefix = (): VNode | null => {
      if (!showPrefix.value) return null

      return (
        <div class={prefixClasses.value} onClick={handlePrefixClick}>
          {props.prefixIcon && (
            <SpIcon
              name={props.prefixIcon}
              size={iconSize.value}
              class={prefixIconClasses.value}
            />
          )}
          {slots.prefix?.()}
        </div>
      )
    }

    // 渲染后缀区域
    const renderSuffix = (): VNode | null => {
      if (!showSuffix.value) return null

      return (
        <div class={suffixClasses.value} onClick={handleSuffixClick}>
          {/* 清除按钮 */}
          {showClearIcon.value && (
            <span
              class={clearIconClasses.value}
              onClick={handleClear}
              onMousedown={(e: MouseEvent) => e.preventDefault()}
              style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center' }}
            >
              <SpIcon
                name="CloseCircle"
                size={iconSize.value}
                clickable={true}
              />
            </span>
          )}

          {/* 加载状态 */}
          {props.loading && (
            <SpIcon
              name="Loading"
              size={iconSize.value}
              class="sp-input__loading"
            />
          )}

          {/* 自定义后缀图标 */}
          {props.suffixIcon && !props.loading && (
            <SpIcon
              name={props.suffixIcon}
              size={iconSize.value}
              class={suffixIconClasses.value}
            />
          )}

          {/* 字数统计 */}
          {props.showWordLimit && props.maxlength && (
            <span class={wordCountClasses.value}>
              {wordCount.value}/{props.maxlength}
            </span>
          )}

          {/* 后缀插槽 */}
          {slots.suffix?.()}
        </div>
      )
    }

    // 暴露的方法
    const focus = () => {
      inputRef.value?.focus()
    }

    const blur = () => {
      inputRef.value?.blur()
    }

    const select = () => {
      inputRef.value?.select()
    }

    expose({
      wrapperRef,
      inputRef,
      focus,
      blur,
      select,
    })

    // 主渲染函数
    return () => (
      <div class={rootClasses.value}>
        <div
          ref={wrapperRef}
          class={wrapperClasses.value}
          onClick={handleWrapperClick}
        >
          {renderPrefix()}
          
          {/* 核心输入区域 */}
          <div class={innerClasses.value}>
            {/* inner 插槽内容 - 用于多选标签或 textarea 等 */}
            {slots.inner?.()}

            {/* 只有在非 display 模式下才渲染 input 元素 */}
            {props.mode !== 'display' && (
              <input
                ref={inputRef}
                type={props.type}
                value={props.value}
                placeholder={props.placeholder}
                disabled={props.disabled}
                readonly={props.readonly}
                maxlength={props.maxlength}
                onInput={handleInput}
                onChange={handleChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onKeydown={handleKeydown}
              />
            )}
          </div>

          {renderSuffix()}
        </div>

        {/* 验证消息 */}
        {slots.message?.() ||
          (props.validateMessage && (
            <div class={['sp-input__message', `sp-input__message--${props.validateState || 'error'}`]}>
              {props.validateMessage}
            </div>
          ))}
      </div>
    )
  },
})
