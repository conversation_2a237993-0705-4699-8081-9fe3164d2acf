<!--
  SelectField.vue - 直接复用 InputField 组件
  简单直接，给 InputField 添加选择器功能
-->

<template>
  <div
    class="select-field-wrapper"
    @click="handleClick"
  >
    <!-- 直接使用 InputField，享受所有功能 -->
    <InputField
      ref="inputFieldRef"
      v-bind="inputFieldProps"
      :value="displayValue"
      readonly
      @clear="handleClear"
    >
      <!-- 后缀：下拉箭头 -->
      <template #suffix>
        <!-- 覆盖默认的 suffixIcon，使用我们自己的下拉箭头 -->
        <sp-icon
          name="ChevronDown"
          :size="16"
          :class="arrowClasses"
        />
      </template>
    </InputField>

    <!-- 下拉选项面板 - 复用普通 select 的 SelectDropdown 组件 -->
    <SelectDropdown
      ref="dropdownRef"
      :visible="isDropdownOpen"
      :options="props.options || []"
      prefix-cls="sp-select"
      :dropdown-style="dropdownStyle"
      :value="props.value"
      :multiple="props.multiple"
      :value-key="props.valueKey || 'value'"
      :label-key="props.labelKey || 'label'"
      :empty-text="props.emptyText || '暂无数据'"
      :variant="props.variant"
      :size="props.size"
      @option-click="handleSelectOption"
      @mousedown.prevent.stop
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
  import InputField from '../input-field/input-field.vue'
  import SpIcon from '../icon/Icon.vue'
  import SelectDropdown from '../select/internal/SelectDropdown.vue'
  import type { SelectFieldProps, SelectFieldEmits } from './types'
  import { selectFieldPropsDefaults } from './types'
  import type { SelectOption } from '../select/select'

  /**
   * SelectField 组件 - 直接复用 InputField
   *
   * 超简单的实现：
   * 1. 直接使用 InputField 组件
   * 2. 添加下拉选项功能
   * 3. 享受所有现有特性
   */

  const props = defineProps<SelectFieldProps>()
  const emit = defineEmits<SelectFieldEmits>()

  // 组件引用
  const inputFieldRef = ref<InstanceType<typeof InputField> | null>(null)
  const dropdownRef = ref<InstanceType<typeof SelectDropdown> | null>(null)

  // 状态
  const isDropdownOpen = ref(false)
  const dropdownStyle = ref({})
  const isSelecting = ref(false) // 添加选择状态标记

  // 计算属性
  const selectedValues = computed(() => {
    if (props.multiple) {
      return Array.isArray(props.value) ? props.value : []
    }
    return props.value !== undefined ? [props.value] : []
  })

  const displayValue = computed(() => {
    if (props.multiple) {
      const labels = selectedValues.value.map(value => {
        const option = props.options?.find(opt => getOptionValue(opt) === value)
        return option ? getOptionLabel(option) : String(value)
      })
      return labels.join(', ')
    } else {
      const option = props.options?.find(
        opt => getOptionValue(opt) === props.value
      )
      return option ? getOptionLabel(option) : ''
    }
  })

  const arrowClasses = computed(() => {
    const classes = ['select-arrow']
    if (isDropdownOpen.value) {
      classes.push('select-arrow--reverse')
    }
    return classes.join(' ')
  })

  // 传递给 InputField 的属性
  const inputFieldProps = computed(() => ({
    label: props.label,
    name: props.name,
    placeholder: props.placeholder,
    required: props.required,
    disabled: props.disabled,
    size: (props.size === 'default' ? 'medium' : props.size) as
      | 'small'
      | 'medium'
      | 'large',
    // 只传递 InputField 支持的 variant，忽略 error 等不支持的属性
    rules: props.rules,
    showMessage: props.showMessage,
    helperText: props.helperText,
    persistentLabel: props.persistentLabel,
    clearable: props.clearable,
    variant: props.variant,
  }))

  // 工具函数
  const getOptionValue = (option: SelectOption) => {
    return option[props.valueKey || 'value']
  }

  const getOptionLabel = (option: SelectOption) => {
    return option[props.labelKey || 'label']
  }

  // 这些工具函数现在由 SelectDropdown 内部处理，不再需要

  // 事件处理
  const handleClick = (event: MouseEvent) => {
    if (props.disabled) return

    // 如果点击的是下拉框内部，不处理
    const dropdownEl =
      dropdownRef.value?.$el ||
      (dropdownRef.value as any)?.$el?.querySelector?.('.sp-select__dropdown')
    if (dropdownEl && dropdownEl.contains(event.target as Node)) {
      return
    }

    isDropdownOpen.value = !isDropdownOpen.value

    if (isDropdownOpen.value) {
      emit('focus')
      // 确保输入框也获得焦点
      nextTick(() => {
        updateDropdownPosition()
        inputFieldRef.value?.focus?.()
      })
    } else {
      // 只有在非选择状态下才触发blur
      if (!isSelecting.value) {
        emit('blur')
      }
    }
  }

  const handleSelectOption = (option: SelectOption) => {
    console.log('🎯 选择选项开始', {
      option,
      isDropdownOpen: isDropdownOpen.value,
    })

    if (option.disabled) return

    // 设置选择状态标记
    isSelecting.value = true
    console.log('🔒 设置选择状态标记为 true')

    const optionValue = getOptionValue(option)
    let newValue: string | number | Array<string | number> | undefined

    if (props.multiple) {
      const currentValues = Array.isArray(props.value) ? props.value : []
      const index = currentValues.indexOf(optionValue)

      if (index > -1) {
        newValue = currentValues.filter(v => v !== optionValue)
      } else {
        newValue = [...currentValues, optionValue]
      }

      emit('update:value', newValue)
      emit('change', newValue)
      console.log('📋 多选模式更新值', newValue)

      // 多选模式下保持焦点和下拉框打开
      setTimeout(() => {
        console.log('🔓 多选模式重置选择标记')
        isSelecting.value = false
        inputFieldRef.value?.focus?.()
        console.log('🎯 多选模式重新聚焦完成')
      }, 0)
    } else {
      newValue = optionValue

      emit('update:value', newValue)
      emit('change', newValue)
      console.log('📝 单选模式更新值', newValue)

      // 单选模式：立即处理，不使用延迟
      nextTick(() => {
        console.log('🚪 关闭下拉框')
        isDropdownOpen.value = false

        nextTick(() => {
          console.log('🔓 重置选择标记')
          isSelecting.value = false

          // 强制重新聚焦
          console.log('🎯 强制重新聚焦到输入框')
          inputFieldRef.value?.focus?.()

          // 确保焦点事件被触发
          setTimeout(() => {
            console.log('🎯 二次确认聚焦')
            inputFieldRef.value?.focus?.()
          }, 10)
        })
      })
    }
  }

  const handleClear = () => {
    const newValue = props.multiple ? [] : undefined
    emit('update:value', newValue)
    emit('clear')
  }

  // 下拉框位置
  const updateDropdownPosition = () => {
    if (!inputFieldRef.value) return

    const rect = inputFieldRef.value.$el.getBoundingClientRect()

    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 4}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999,
      // 禁用动画，避免飘动效果
      transition: 'none',
      transform: 'none',
    }
  }

  // 点击外部关闭
  const handleClickOutside = (event: MouseEvent) => {
    // 如果正在选择中，完全忽略外部点击
    if (isSelecting.value) {
      console.log('⏸️ 正在选择中，忽略外部点击')
      return
    }

    const target = event.target as Node

    // 检查是否点击了下拉框内部 - 需要找到实际的 DOM 元素
    const dropdownEl =
      dropdownRef.value?.$el ||
      (dropdownRef.value as any)?.$el?.querySelector?.('.sp-select__dropdown')
    if (dropdownEl && dropdownEl.contains(target)) {
      console.log('📦 点击了下拉框内部，忽略')
      return
    }

    // 检查是否点击了输入框自身
    if (inputFieldRef.value && inputFieldRef.value.$el.contains(target)) {
      console.log('📝 点击了输入框自身，忽略')
      return
    }

    // 只有真正的外部点击才关闭并失焦
    if (isDropdownOpen.value) {
      console.log('🌍 真正的外部点击，关闭下拉框并失焦')
      isDropdownOpen.value = false
      emit('blur')
    }
  }

  // 处理窗口滚动和调整大小
  const handleResize = () => {
    if (isDropdownOpen.value) {
      updateDropdownPosition()
    }
  }

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    window.addEventListener('scroll', handleResize, true)
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
    window.removeEventListener('scroll', handleResize, true)
    window.removeEventListener('resize', handleResize)
  })

  // 暴露方法
  const focus = () => inputFieldRef.value?.focus?.()
  const blur = () => inputFieldRef.value?.blur?.()
  const clear = () => handleClear()
  const validate = () => inputFieldRef.value?.validate?.()
  const resetField = () => inputFieldRef.value?.resetField?.()
  const clearValidate = () => inputFieldRef.value?.clearValidate?.()

  defineExpose({
    focus,
    blur,
    clear,
    validate,
    resetField,
    clearValidate,
    get select() {
      return inputFieldRef.value?.$el || null
    },
    get wrapper() {
      return inputFieldRef.value?.wrapper || null
    },
  })
</script>

<style scoped>
  .select-field-wrapper {
    position: relative;
    width: 100%;
  }

  .select-arrow {
    color: #6b7280;
    transition: transform 0.2s ease;
    cursor: pointer;
  }

  .select-arrow--reverse {
    transform: rotate(180deg);
  }

  /* 下拉框样式现在由 SelectDropdown 组件提供 */

  /* 覆盖 SelectDropdown 的动画效果，避免飘动 */
  :deep(.sp-select-dropdown-enter-active),
  :deep(.sp-select-dropdown-leave-active) {
    transition: none !important;
  }

  :deep(.sp-select-dropdown-enter-from),
  :deep(.sp-select-dropdown-leave-to) {
    opacity: 1 !important;
    transform: none !important;
  }
</style>
