import { computed } from 'vue'
import { useBEM } from '@speed-ui/bem-helper'
import type { TextareaProps } from '../components/textarea/types'

interface LogicState {
  computedDisabled: { value: boolean | undefined }
  isFocused: { value: boolean }
  hasValue?: { value: boolean }
  showPrefix?: { value: boolean }
  showSuffix?: { value: boolean }
}

export function useTextareaStyles(
  props: TextareaProps,
  logicState: LogicState
) {
  const bem = useBEM('textarea')

  // ===== 图标尺寸计算 =====
  const iconSize = computed(() => {
    switch (props.size) {
      case 'small':
        return 14
      case 'large':
        return 18
      case 'medium':
      default:
        return 16
    }
  })

  // ===== 根容器类名 =====
  const rootClasses = computed(() => [
    bem.b(),
    bem.m(props.size || 'medium'),
    bem.m(props.variant || 'default'),
    bem.m(`effect-${props.effect || 'none'}`),
    bem.m(`resize-${props.resize || 'vertical'}`),
    {
      [bem.m('disabled')]: logicState.computedDisabled.value || false,
      [bem.m('readonly')]: props.readonly,
      [bem.m('focused')]: logicState.isFocused?.value || false,
      [bem.m('error')]: props.error || props.validateState === 'error',
      [bem.m('warning')]: props.validateState === 'warning',
      [bem.m('success')]: props.validateState === 'success',
      [bem.m('loading')]: props.loading,
      [bem.m('has-value')]: logicState.hasValue?.value || false,
      [bem.m('has-prefix')]: logicState.showPrefix?.value || !!props.prefixIcon,
      [bem.m('has-suffix')]:
        logicState.showSuffix?.value || !!props.suffixIcon || props.clearable,
      [bem.m('autosize')]: !!props.autosize,
    },
  ])

  // ===== 各部分类名 =====
  const wrapperClasses = computed(() => [bem.e('wrapper')])

  const innerClasses = computed(() => [bem.e('inner')])

  const elementClasses = computed(() => [bem.e('element')])

  const prefixClasses = computed(() => [bem.e('prefix')])

  const suffixClasses = computed(() => [bem.e('suffix')])

  const prefixIconClasses = computed(() => [bem.e('prefix-icon')])

  const suffixIconClasses = computed(() => [bem.e('suffix-icon')])

  const clearIconClasses = computed(() => [bem.e('clear')])

  const wordCountClasses = computed(() => [bem.e('count')])

  const messageClasses = computed(() => [
    bem.e('message'),
    {
      [bem.em('message', props.validateState || 'error')]: props.validateState,
    },
  ])

  // ===== 为 InputCore 准备的属性（Textarea 模式） =====
  const inputCoreProps = computed(() => ({
    customRootClasses: rootClasses.value,
    customWrapperClasses: wrapperClasses.value,
    customInnerClasses: innerClasses.value,
    customPrefixClasses: prefixClasses.value,
    customSuffixClasses: suffixClasses.value,
  }))

  return {
    // 类名
    rootClasses,
    wrapperClasses,
    innerClasses,
    elementClasses,
    prefixClasses,
    suffixClasses,
    prefixIconClasses,
    suffixIconClasses,
    clearIconClasses,
    wordCountClasses,
    messageClasses,

    // 其他计算属性
    iconSize,

    // 为 InputCore 准备的属性包
    inputCoreProps,
  }
}
